/**
 * 通知处理器
 * 处理通知队列中的待发送消息
 */

const { query } = require('../config/database');
const SubscriptionService = require('./subscriptionService');

class NotificationProcessor {
    constructor() {
        this.subscriptionService = new SubscriptionService();
        this.isProcessing = false;
        this.intervalId = null;
        this.maxRetries = 3;
        this.processingInterval = 5000; // 5秒轮询一次
    }

    /**
     * 启动通知处理器
     * @param {number} intervalMs 轮询间隔（毫秒）
     */
    start(intervalMs = this.processingInterval) {
        if (this.intervalId) {
            console.log('⚠️ 通知处理器已在运行中');
            return;
        }
        
        console.log(`🔔 通知处理器启动，轮询间隔: ${intervalMs}ms`);
        this.intervalId = setInterval(() => {
            this.processNotifications();
        }, intervalMs);

        // 立即执行一次
        this.processNotifications();
    }

    /**
     * 停止通知处理器
     */
    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log('🛑 通知处理器已停止');
        }
    }

    /**
     * 处理待发送通知
     */
    async processNotifications() {
        if (this.isProcessing) {
            return;
        }
        
        this.isProcessing = true;
        try {
            const notifications = await this.getPendingNotifications();
            
            if (notifications.length > 0) {
                console.log(`📋 发现 ${notifications.length} 条待处理通知`);
                
                for (const notification of notifications) {
                    await this.processNotification(notification);
                }
            }
        } catch (error) {
            console.error('❌ 处理通知队列失败:', error.message);
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * 获取待处理通知
     * @returns {Promise<Array>} 待处理通知列表
     */
    async getPendingNotifications() {
        try {
            const sql = `
                SELECT * FROM notification_queue 
                WHERE status = 'PENDING' 
                AND retry_count < ?
                ORDER BY created_time ASC 
                LIMIT 10
            `;
            return await query(sql, [this.maxRetries]);
        } catch (error) {
            console.error('❌ 获取待处理通知失败:', error.message);
            return [];
        }
    }

    /**
     * 处理单个通知
     * @param {Object} notification 通知对象
     */
    async processNotification(notification) {
        try {
            console.log(`🔄 处理通知 ID: ${notification.id}, 类型: ${notification.notification_type}, 订单: ${notification.order_no}`);
            
            // 更新状态为处理中
            await this.updateNotificationStatus(notification.id, 'PROCESSING');

            if (notification.notification_type === 'ORDER_GENERATED') {
                await this.sendOrderGeneratedNotification(notification);
            } else if (notification.notification_type === 'IMAGE_UPLOADED') {
                await this.sendImageUploadedNotification(notification);
            } else {
                throw new Error(`未知的通知类型: ${notification.notification_type}`);
            }

            // 标记为已发送
            await this.updateNotificationStatus(notification.id, 'SENT');
            console.log(`✅ 通知发送成功 ID: ${notification.id}`);
            
        } catch (error) {
            console.error(`❌ 处理通知失败 (ID: ${notification.id}):`, error.message);
            
            // 增加重试次数
            await this.updateNotificationRetry(notification.id, error.message);
        }
    }

    /**
     * 发送订单生成通知
     * @param {Object} notification 通知对象
     */
    async sendOrderGeneratedNotification(notification) {
        try {
            // 查找订阅了该工厂的用户
            const subscribers = await this.getFactorySubscribers(notification.receiver);
            
            if (subscribers.length === 0) {
                console.log(`⚠️ 工厂 ${notification.receiver} 没有订阅用户，跳过发送`);
                return;
            }

            console.log(`📤 向 ${subscribers.length} 个用户发送订单生成通知`);

            const messageData = {
                character_string1: { value: notification.order_no }, // 订单号
                thing2: { value: notification.receiver || '客户' }, // 客户名称
                thing11: { value: notification.follower || '跟单员' }, // 当前处理人
                phrase13: { value: '订单已生成' }, // 订单状态
                time31: { value: this.formatDateTime(notification.created_at) } // 生成时间
            };

            // 向所有订阅用户发送通知
            const sendPromises = subscribers.map(subscriber => 
                this.subscriptionService.wechatService.sendSubscribeMessage(
                    subscriber.openid,
                    messageData,
                    `pages/order/order?orderNumber=${encodeURIComponent(notification.order_no)}`
                )
            );

            const results = await Promise.allSettled(sendPromises);
            
            // 统计发送结果
            const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
            const failCount = results.length - successCount;
            
            console.log(`📊 订单生成通知发送完成: 成功 ${successCount}, 失败 ${failCount}`);
            
            if (failCount > 0) {
                const errors = results
                    .filter(r => r.status === 'rejected' || !r.value.success)
                    .map(r => r.reason || r.value.error)
                    .join('; ');
                throw new Error(`部分发送失败: ${errors}`);
            }

        } catch (error) {
            console.error('❌ 发送订单生成通知失败:', error.message);
            throw error;
        }
    }

    /**
     * 发送图片上传通知（复用现有逻辑）
     * @param {Object} notification 通知对象
     */
    async sendImageUploadedNotification(notification) {
        try {
            await this.subscriptionService.sendOrderImageUploadNotification(
                notification.order_no, 
                notification.receiver
            );
        } catch (error) {
            console.error('❌ 发送图片上传通知失败:', error.message);
            throw error;
        }
    }

    /**
     * 获取工厂订阅用户
     * @param {string} factoryName 工厂名称
     * @returns {Promise<Array>} 订阅用户列表
     */
    async getFactorySubscribers(factoryName) {
        try {
            const sql = `
                SELECT DISTINCT openid, username, factory_name
                FROM user_subscriptions 
                WHERE factory_name = ? AND status = 1
            `;
            return await query(sql, [factoryName]);
        } catch (error) {
            console.error('❌ 获取工厂订阅用户失败:', error.message);
            return [];
        }
    }

    /**
     * 更新通知状态
     * @param {number} notificationId 通知ID
     * @param {string} status 新状态
     */
    async updateNotificationStatus(notificationId, status) {
        try {
            const sql = `
                UPDATE notification_queue 
                SET status = ?, processed_at = NOW(), updated_time = NOW()
                WHERE id = ?
            `;
            await query(sql, [status, notificationId]);
        } catch (error) {
            console.error('❌ 更新通知状态失败:', error.message);
            throw error;
        }
    }

    /**
     * 更新通知重试信息
     * @param {number} notificationId 通知ID
     * @param {string} errorMessage 错误信息
     */
    async updateNotificationRetry(notificationId, errorMessage) {
        try {
            const sql = `
                UPDATE notification_queue 
                SET retry_count = retry_count + 1, 
                    error_message = ?,
                    status = CASE 
                        WHEN retry_count + 1 >= ? THEN 'FAILED' 
                        ELSE 'PENDING' 
                    END,
                    updated_time = NOW()
                WHERE id = ?
            `;
            await query(sql, [errorMessage, this.maxRetries, notificationId]);
        } catch (error) {
            console.error('❌ 更新通知重试信息失败:', error.message);
        }
    }

    /**
     * 格式化日期时间
     * @param {Date|string} dateTime 日期时间
     * @returns {string} 格式化后的日期时间
     */
    formatDateTime(dateTime) {
        if (!dateTime) return new Date().toLocaleString('zh-CN');
        
        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * 获取处理器状态
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            isRunning: !!this.intervalId,
            isProcessing: this.isProcessing,
            processingInterval: this.processingInterval,
            maxRetries: this.maxRetries
        };
    }
}

module.exports = NotificationProcessor;
