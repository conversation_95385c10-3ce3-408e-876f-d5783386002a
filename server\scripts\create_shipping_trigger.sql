-- 创建shipping_detail表新增数据触发器
-- 当有新订单数据插入时，自动发送订阅消息

USE identify;

-- 删除已存在的触发器（如果有）
DROP TRIGGER IF EXISTS tr_shipping_detail_insert;

-- 删除已存在的存储过程（如果有）
DROP PROCEDURE IF EXISTS sp_send_order_notification;

DELIMITER $$

-- 创建发送订单通知的存储过程
CREATE PROCEDURE sp_send_order_notification(
    IN p_order_no VARCHAR(100),
    IN p_receiver VARCHAR(100),
    IN p_follower VARCHAR(100),
    IN p_created_at DATETIME
)
BEGIN
    DECLARE v_api_url VARCHAR(500);
    DECLARE v_json_data TEXT;
    DECLARE v_curl_command TEXT;
    DECLARE v_result INT DEFAULT 0;
    
    -- 构建API URL（使用HTTPS协议）
    SET v_api_url = 'https://www.mls2005.top/api/subscription/send-order-created-notification';
    
    -- 构建JSON数据
    SET v_json_data = CONCAT(
        '{"orderNo":"', IFNULL(p_order_no, ''), 
        '","receiver":"', IFNULL(p_receiver, ''),
        '","follower":"', IFNULL(p_follower, ''),
        '","createdAt":"', IFNULL(DATE_FORMAT(p_created_at, '%Y-%m-%d %H:%i:%s'), ''),
        '","orderStatus":"订单已生成"}'
    );
    
    -- 记录触发日志
    INSERT INTO subscription_trigger_logs (
        order_no, 
        receiver, 
        api_url, 
        json_data, 
        trigger_time, 
        status
    ) VALUES (
        p_order_no, 
        p_receiver, 
        v_api_url, 
        v_json_data, 
        NOW(), 
        'PENDING'
    );
    
    -- 构建curl命令（异步调用）
    SET v_curl_command = CONCAT(
        'curl -X POST "', v_api_url, '" ',
        '-H "Content-Type: application/json" ',
        '-d ''', v_json_data, ''' ',
        '> /dev/null 2>&1 &'
    );
    
    -- 执行HTTP请求（异步）
    -- 注意：这需要MySQL配置允许执行系统命令
    -- SET v_result = sys_exec(v_curl_command);
    
    -- 更新日志状态
    UPDATE subscription_trigger_logs 
    SET status = 'SENT', sent_time = NOW() 
    WHERE order_no = p_order_no 
    AND trigger_time = (
        SELECT MAX(trigger_time) 
        FROM (SELECT trigger_time FROM subscription_trigger_logs WHERE order_no = p_order_no) t
    );
    
END$$

-- 创建触发器
CREATE TRIGGER tr_shipping_detail_insert
    AFTER INSERT ON shipping_detail
    FOR EACH ROW
BEGIN
    -- 调用发送通知的存储过程
    CALL sp_send_order_notification(
        NEW.order_no,
        NEW.receiver,
        NEW.follower,
        NEW.created_at
    );
END$$

DELIMITER ;

-- 创建触发器日志表
CREATE TABLE IF NOT EXISTS subscription_trigger_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(100) NOT NULL,
    receiver VARCHAR(100),
    api_url VARCHAR(500),
    json_data TEXT,
    trigger_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    sent_time DATETIME NULL,
    status ENUM('PENDING', 'SENT', 'FAILED') DEFAULT 'PENDING',
    error_message TEXT NULL,
    INDEX idx_order_no (order_no),
    INDEX idx_trigger_time (trigger_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订阅消息触发日志表';

-- 查看创建结果
SHOW TRIGGERS LIKE 'shipping_detail';
SELECT 'Trigger and procedure created successfully' as result;
